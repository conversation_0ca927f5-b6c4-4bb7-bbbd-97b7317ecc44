@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 98%;
    --popover-foreground: 0 0% 10%;
    --primary: 0 0% 15%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 92%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 90%;
    --muted-foreground: 0 0% 40%;
    --accent: 240 5% 85%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 80% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 85%;
    --input: 0 0% 90%;
    --ring: 0 0% 20%;
    --radius: 0.75rem;
    --chart-1: 240 5% 40%;
    --chart-2: 240 4% 50%;
    --chart-3: 240 3% 60%;
    --chart-4: 240 2% 70%;
    --chart-5: 240 1% 80%;
  }

  .dark {
    --background: 0 0% 4%; /* <PERSON><PERSON><PERSON> hitam */
    --foreground: 0 0% 88%;
    --card: 0 0% 6%;
    --card-foreground: 0 0% 88%;
    --popover: 0 0% 6%;
    --popover-foreground: 0 0% 88%;
    --primary: 0 0% 95%;
    --primary-foreground: 0 0% 10%;
    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 88%;
    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 55%;
    --accent: 0 0% 14%;
    --accent-foreground: 0 0% 90%;
    --destructive: 0 60% 45%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 18%;
    --input: 0 0% 20%;
    --ring: 0 0% 70%;
    --chart-1: 240 5% 50%;
    --chart-2: 240 4% 60%;
    --chart-3: 240 3% 70%;
    --chart-4: 240 2% 80%;
    --chart-5: 240 1% 90%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  @keyframes spinFade {
    0% {
      transform: scale(0.8) rotate(0deg);
      opacity: 0.4;
    }
    50% {
      transform: scale(1) rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: scale(0.8) rotate(360deg);
      opacity: 0.4;
    }
  }

  .loader {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid transparent;
    border-top-color: #3b82f6;
    animation: spinFade 0.6s linear infinite;
  }

  /* Music Player Slider Styles */
  .slider {
    background: linear-gradient(
      to right,
      hsl(var(--primary)) 0%,
      hsl(var(--primary)) var(--value, 0%),
      hsl(var(--muted)) var(--value, 0%),
      hsl(var(--muted)) 100%
    );
    outline: none;
    transition: all 0.2s ease;
  }

  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: hsl(var(--primary));
    cursor: pointer;
    border: 2px solid hsl(var(--background));
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: hsl(var(--primary));
    cursor: pointer;
    border: 2px solid hsl(var(--background));
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}
