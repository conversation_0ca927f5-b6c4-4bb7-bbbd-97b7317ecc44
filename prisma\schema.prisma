generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id             String           @id @unique
  email          String
  firstName      String
  lastName       String
  GuestBookEntry GuestBookEntry[]
}

model GuestBookEntry {
  id        String   @id @default(uuid())
  message   String
  createdAt DateTime @default(now())
  userId    String?
  User      User?    @relation(fields: [userId], references: [id])
}
