{"contactInfo": {"email": "<EMAIL>", "linkedin": "https://www.linkedin.com/in/khairil-rahman-hakiki", "github": "https://github.com/kyyril", "cv": "https://drive.google.com/file/d/1ZL3U-a7lVt2pz8FFGk8AcFnhv2o4A2U9/view?usp=sharing"}, "education": [{"id": "1", "institution": "State Islamic University of Imam <PERSON><PERSON><PERSON>", "degree": "Information Systems", "description": "Relevant Coursework: Data structures, algorithms, software engineering, databases, and UI/UX design.", "startDate": "2022", "endDate": "Present"}], "experiences": [{"id": "2", "institution": "CodeAtHome", "job": "Junior Frontend Developer", "status": "Freelance", "location": "Lampung, Indonesia", "working": "Remote", "startDate": "April 2025", "endDate": "Present", "description": "Junior Frontend Developer at CodeAtHome — added new features, fixed bugs, and followed clean code practices with help from regular code reviews. One of the features I added was payment integration using Xendit with the backend.", "technologies": ["Typescript", "React.js", "Next.js", "REST-API", "ShadcnUI", "Tanstack Query", "Redux"]}, {"id": "1", "institution": "PT Astra International Tbk - TSO Auto2000", "job": "Software Engineer <PERSON><PERSON>", "status": "Internship", "location": "<PERSON><PERSON><PERSON>, Indonesia", "working": "On-site", "startDate": "Jan 2025", "endDate": "Mar 2025", "description": "As a Software Engineer Intern, I developed a dynamic website with easy content management and AI car recommendations for Toyota Labuhanbatu, and built ReminderApp with role-based access and STNK/BPKB reminders for Toyota Rantauprapat.", "technologies": ["Typescript", "React.js", "Python", "Next.js", "TailwindCSS", "Zustand", "Supabase", "Cloudinary", "ShadcnUI", "REST-API", "Gemini", "Apps<PERSON><PERSON><PERSON><PERSON>", "Vite"]}]}