{"projects": [{"id": "sobat-takwa", "title": "<PERSON><PERSON>", "description": "Sobat Takwa Is a web app providing prayer schedules, Quran reading with audio, and daily prayers with search functionality, designed for ease and convenience.", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI", "Framer-motion"], "live_url": "https://sobat-takwa.vercel.app/", "code_repo_url": "https://github.com/kyyril/sobatTakwa", "type": "Frontend", "category": "Web", "date": "2023-06-10", "features": ["Search and select a city to view prayer times.", "Prayer times based on the selected location.", "Search for verse by verse name.", "Listen to the audio of each verse.", "daily prayer list.", "Search for prayers by prayer name."], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1730290596/1home_wtsi7p.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730290545/2surah_ayb3kx.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730290545/3duo_had3wh.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730453159/ffa2993e-cae4-461f-accd-b4aa1d912bd8.png"]}, {"id": "sync-blog", "title": "SyncBlog", "description": "SyncBlog is a modern blog application that allows users to manage content with rich features like text editing, user authentication, and dynamic post interactions. Users can like, comment, bookmark, and follow profiles. The platform is optimized with Cloudinary, powered by PostgreSQL, and hosted via Railway.", "technologies": ["Typescript", "Tanstack Query", "TailwindCSS", "ShadcnUI", "Next.js", "<PERSON><PERSON><PERSON>", "Motion", "Redux", "A<PERSON>os", "Mdx", "<PERSON><PERSON>", "Vercel", "Multirepo", "Express.js", "PostgreSQL", "Cloudinary", "Prisma", "Railway", "Jwt"], "live_url": "https://syncblog.vercel.app", "code_repo_url": "https://github.com/kyyril/fr-blogs", "type": "Fullstack", "category": "Web", "date": "2024-05-12", "features": ["User authentication", "Create, edit, and delete blog posts", "Rich text editing with MDX", "Category-based filtering of blog posts", "User profiles with follow and unfollow functionality", "Comment system on blog posts", "Like and unlike blog posts", "Bookmark blog posts", "Count blog visitors"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1750526245/9c704775-d33c-4ee6-a459-40c836b6379f.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526388/88e5c6d9-1f27-4714-9c33-0a4d1b62dc1f.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526446/d2dcfe99-03e5-42ae-b537-fa0e997b3906.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526495/b1569478-7bcd-45eb-ac0c-3f5f3e5edae1.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526541/6a90f279-1eb9-4396-be5c-a0341205151c.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526568/68408b80-138c-434d-94b1-4d56134ae87c.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526568/68408b80-138c-434d-94b1-4d56134ae87c.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526669/86b02c7c-53f7-4c27-a02b-aef0067fd1ea.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750526756/6a770acf-c63c-4fc9-b566-b4ccd1d70872.png"]}, {"id": "cihuy-movie", "title": "Cihuy Movie", "technologies": ["Typescript", "React-router", "Context", "Vite", "ChakraUI", "Firebase-auth", "A<PERSON>os"], "live_url": "https://cihuymovie.vercel.app", "code_repo_url": "https://github.com/kyyril/cihuymovie", "type": "Fullstack", "category": "Web", "date": "2023-06-10", "description": "Cihuy Movie is a web app for discovering and organizing popular movies and TV shows, featuring daily and weekly trending lists. Built with TypeScript, React-router, Vite, and ChakraUI, it includes discovery pages with pagination, a search function, and Firebase Authentication, allowing users to create a personalized watchlist.", "features": ["Trending Lists: Daily/weekly popular movies and shows.", "Discovery Pages: Browse content with pagination.", "Search: Quickly find movies or shows.", "Watchlist: Save favorites to a personalized list."], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1730292014/1home_2_cegeif.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730292012/Video_Screen1730266542360_fkebhs.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730292012/Video_Screen1730266542360_fkebhs.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730291994/Video_Screen1730266576448_fbeatf.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730291952/Video_Screen1730266594686_hfznyo.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730453517/57065083-6cfe-48cd-9331-23f72389fda1.png"]}, {"id": "design-to-code", "title": "Design To Code", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI", "Gemini", "Cloudinary", "PostgreSQL", "Open-router", "Firebase-auth"], "live_url": "https://designtocode-kohl.vercel.app", "code_repo_url": "https://github.com/kyyril/designtocode", "type": "Fullstack", "category": "Web", "date": "2023-06-10", "description": "Design to Code is a website that converts your design into React.js code. Simply upload an image of your design, and the system will generate the corresponding code.", "features": ["Generate Code – Automatically convert your design into clean, structured React.js code.", "Regenerate Code – If you're not satisfied, regenerate the code with improvements.", "AI Model Selection – Choose from different AI models to get the best conversion results.", "Design History – View and manage your past design conversions."], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1739379881/homepage_uwzzae.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739379881/work_bujo40.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739379882/design_ycpxvx.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739379881/testi1_myvo4g.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739379881/testi2_jkbqop.png"]}, {"id": "we-share", "title": "WeShare", "technologies": ["Javascript", "React Native", "Expo", "Supabase"], "live_url": "https://expo.dev/accounts/kyyril.hrp/projects/weshare/builds/98e1cc32-30a4-4332-a869-5567b32d58bf", "code_repo_url": "https://github.com/kyyril/weshare", "type": "Fullstack", "category": "Mobile", "date": "2025-01-05", "description": "WeShare is a social media app where users can create, edit, and delete posts, comment on posts, get notifications, and edit their profile. The app also supports real-time data updates.", "features": ["Create, read, update, and delete (CRUD) posts", "Comment on posts", "Notification feature for new activities", "Edit and update user profile", "Real-time data updates"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1746208810/43b2e80d-b6b5-456a-a87d-9e811d8b8232.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1746208916/d4f6b935-08f1-402b-a313-8bede3d49965.png"]}, {"id": "manytools", "title": "ManyTools", "description": "ManyTools is a comprehensive web app offering AI-powered tools for text processing and academic paper generation. Built with Next.js 15 and integrated with Google's Gemini AI.", "technologies": ["Typescript", "Next.js", "React.js", "TailwindCSS", "ShadcnUI", "Framer Motion", "Zustand", "Supabase", "Gemini"], "live_url": "https://manytools-three.vercel.app/", "code_repo_url": "https://github.com/kyyril/manytools", "type": "Fullstack", "category": "Web", "date": "2024-06-10", "features": ["AI Paraphrase, <PERSON><PERSON><PERSON><PERSON>, Grammar Check, Plagiarism Detector", "Free token system with ad-based reward", "Authentication via Supabase"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1750710834/4d0ae2e1-ac14-4323-be2f-627fb39b75d0.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750710881/32a524ea-a528-4d0a-a28c-4b2bce29ebc4.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750710932/ca5d1557-1ea6-4218-8215-79a62a9670d0.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750710937/46af68aa-5a25-49c7-b1e7-f4e069321e76.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1750710987/c58fb6e6-36a7-4171-ba7b-97ce20fb287a.png"]}, {"id": "toyota-labuhanbatu", "title": "Toyota LabuhanBatu", "description": "The Toyota Labuhanbatu is a website that makes it easy to update content using Google Spreadsheet. This allows users to manage text, images, and other content without technical skills.", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI", "REST-API", "Gemini", "Cloudinary", "Apps<PERSON><PERSON><PERSON><PERSON>", "Zustand"], "live_url": "https://www.toyotarantauprapat.com", "code_repo_url": "/projects/toyota-labuhanbatu", "type": "Fullstack", "category": "Web", "date": "2025-01-02", "features": ["Dynamic Content Management", "Beranda – Highlights the brand, latest promotions, and key updates.", "Mobil – The Cars page shows a list of Toyota models with a filter option to help users find specific cars. Users can also calculate a credit simulation based on the selected car type.", "Layanan – Displays information about Toyota services, updated automatically.", "Rekomendasi AI – toyota car recommendation based on user preference using AI"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1740494090/d85c48c5-a453-41d4-8569-7710487badf4.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1740493600/488c1c41-3861-4cd7-aec4-97f13bf14672.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1738382101/car_n93lpk.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1738382101/car_detail_ewp3vb.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1738382102/layanan_nuiqeb.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1740493003/b5b7ac42-b347-4a0f-a91a-834cd272ede0.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739695004/f15d22a9-a447-46db-b857-0eae42f1c80d.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1739695177/f6db3acb-9cc4-4ae6-817c-c96e4c386fe4.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1740494043/dcdb98b6-9eec-4bbb-b7a6-e9893310f526.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1740494476/d1a4c284-6bd9-4865-baac-7133c07f2156.png"]}, {"id": "gemini-fine-tuning-studio", "title": "Gemini Fine-tuning Studio", "description": "A modern, intuitive web application for creating and managing fine-tuned Google Gemini models. Built with React, TypeScript, and Tailwind CSS.", "technologies": ["React.js", "TypeScript", "TailwindCSS", "Vite", "Context"], "live_url": "https://gemini-fine-tuning-studio.vercel.app", "code_repo_url": "https://github.com/kyyril/gemini-fine-tuning-studio", "type": "Frontend", "category": "Web", "date": "2025-06-09", "features": ["Secure API Key Management: Store your Gemini API key locally", "Training Data Editor: Create, edit, and manage training examples with a visual interface", "Flexible Data Import/Export: Upload training data from JSON files or export your datasets", "Model Creation: Fine-tune Gemini models with customizable hyperparameters", "Real-time Progress Monitoring: Track training progress with visual indicators", "Model Testing: Test your fine-tuned models directly in the interface", "Model Management: List, test, and delete your fine-tuned models", "Modern UI: Beautiful, responsive design with smooth animations", "Smart Notifications: Real-time feedback with toast notifications"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1749449892/81faf3e7-024a-499b-b087-7aabf9c51f76.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1749449736/ec45af41-b063-4676-8df8-a3b9900823b3.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1749449855/acb1ce25-7ce7-46f0-a5b9-fb0fa3bb2f96.png"]}, {"id": "instacook", "title": "InstaCook", "description": "Roastgram is a web app that delivers sarcastic Instagram profile roasts based on the username you provide.", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI", "Framer-motion", "Gemini", "ApiFy"], "live_url": "https://instacook.vercel.app", "code_repo_url": "https://github.com/kyyril/roastapp", "type": "Fullstack", "category": "Web", "date": "2025-01-02", "features": ["AI Instagram Roasting – Get sarcastic comments based on Instagram usernames.", "Username Analysis – Processes data for relevant roasting.", "Sarcastic Responses – Powered by Gemini AI for witty comebacks.", "Interactive Animations – Enhanced with Framer Motion for a lively experience.", "Modern Design – Stylish and responsive UI with Tailwind CSS.", "Data Scraping – Uses Apify to fetch profile data."], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1742616342/25c533d9-bfad-45e5-ba3b-3b1971d907c4.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1742616442/a3a9671d-9496-4adc-8519-9b67a33dae0c.png"]}, {"id": "saas-notesapp", "title": "Saas NotesApp", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI", "Stripe", "<PERSON><PERSON>-auth", "Prisma", "Supabase", "PostgreSQL"], "live_url": "https://saas-noteapp.vercel.app", "code_repo_url": "https://github.com/kyyril/saas-noteapp", "type": "Fullstack", "category": "Web", "date": "2023-06-10", "description": "This project is a SaaS application built with a modern tech stack including Next.js 14, <PERSON>e for payments, Kinde for authentication, Prisma for database management, Supabase for backend functionality, and Tailwind CSS for styling. The application allows users to subscribe to premium services and manage their notes. Free-tier users are limited to creating a maximum of 5 notes, after which they need to subscribe for unlimited access.", "features": ["CRUD for Notes (Add, View, Edit, Delete Notes)", "Server-side Implementation", "Stripe Customer Portal", "theme color can be customized"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1730293831/1home_3_jsr5ge.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730448744/Video_Screen1730448720255_eiwe4h.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730448799/Video_Screen1730448777220_qvqgfa.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1730450907/e060a763-6a49-4f82-8140-6691a486effe.png"]}, {"id": "hadith-api", "title": "Hadith API", "description": "Hadith API with 9 narrators, BAHASA translation stored in local JSON file.", "technologies": ["Golang", "Gin", "Swagger", "REST-API"], "live_url": "https://hadith-api-go.vercel.app", "code_repo_url": "https://github.com/kyyril/hadith-api", "type": "Backend", "category": "All", "date": "2023-06-10", "features": ["Get hadiths by narrator (/api/v1/hadis/:slug)", "Get a specific hadith by narrator and number (/api/v1/hadis/:slug/:number)", "Get list of available narrators (/api/v1/narrators)", "Pagination and search support", "Swagger documentation"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1747006302/8b747e8a-50f0-43ba-8b8d-81a2a5f99ed7.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1747006240/6193c862-73ee-4600-b0e4-5616199a3ee0.png", "https://res.cloudinary.com/da5ggxk01/image/upload/v1747006178/6f9c345d-bfe4-425e-9fd0-dc89f0892b17.png"]}, {"id": "aku-<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "description": "A web app for searching and displaying student info from Indonesian universities.", "technologies": ["Typescript", "Next.js", "TailwindCSS", "ShadcnUI"], "live_url": "https://akumahasigma.vercel.app/mhs", "code_repo_url": "https://github.com/kyyril/cari-data-mahasigma", "type": "Frontend", "category": "Web", "date": "2023-06-10", "features": ["feat1", "feat2", "feat3"], "image": ["https://res.cloudinary.com/da5ggxk01/image/upload/v1730253623/akuMahasigma_mksoo2.png"]}]}