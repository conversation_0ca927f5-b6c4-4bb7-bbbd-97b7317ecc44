{"name": "personal-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@kinde-oss/kinde-auth-nextjs": "^2.3.8", "@octokit/rest": "^22.0.0", "@prisma/client": "^5.19.1", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.2.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.1", "framer-motion": "^11.11.7", "lucide-react": "^0.441.0", "next": "^14.2.29", "next-cloudinary": "^6.16.0", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-lazy-load-image-component": "^1.6.2", "react-markdown": "^9.0.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.12", "postcss": "^8", "prisma": "^5.19.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}