import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Navigation } from "@/components/BottomBar";
import { ThemeProvider } from "@/components/ThemeProvider";
import FooterWrapper from "@/components/FooterWrapper";
import { Analytics } from "@vercel/analytics/next";

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> | Frontend Developer",
  description:
    "I'm an Information Systems student who loves programming, especially software web development. I specialize in Next.js with Typescript and am currently learning backend development with Golang.",
  keywords: [
    "<PERSON><PERSON><PERSON>",
    "Frontend Developer",
    "Web Development",
    "Next.js",
    "TypeScript",
    "React",
    "Software Engineer",
    "Information Systems",
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "<PERSON><PERSON><PERSON>",
  robots: "index, follow",
  verification: {
    google: "24f9cc081f9ae37b",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://kyyril.vercel.app",
    siteName: "<PERSON><PERSON><PERSON>",
    title: "<PERSON><PERSON><PERSON> | Frontend Developer",
    description: "Frontend Developer specializing in Next.js and TypeScript",
  },
  twitter: {
    title: "Khairil Rahman <PERSON>kiki | Frontend Developer",
    description: "Frontend Developer specializing in Next.js and TypeScript",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <title>khairil rahman hakiki</title>
      </head>
      <body className={`antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Navigation />
          {children}
          <Analytics />
          <FooterWrapper />
        </ThemeProvider>
      </body>
    </html>
  );
}
